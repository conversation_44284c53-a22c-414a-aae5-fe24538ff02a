/* Reset và base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 480px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
}

header h1 {
    font-size: 2.2rem;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

header p {
    color: #b8c5d6;
    font-size: 1rem;
}

/* Sections */
section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

section h2 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #ffd700;
}

/* Settings */
.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    color: #e0e6ed;
    font-weight: 500;
}

.setting-group input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.setting-group input:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #b8c5d6;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffd700;
}

/* Prediction */
.prediction-card {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.prediction-main {
    text-align: center;
    margin-bottom: 20px;
}

.prediction-label {
    display: block;
    font-size: 1rem;
    color: #e0e6ed;
    margin-bottom: 10px;
}

.prediction-value {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.prediction-details {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.detail-item span:first-child {
    color: #b8c5d6;
}

.detail-item span:last-child {
    color: #ffd700;
    font-weight: bold;
}

/* Buttons */
.btn-primary, .btn-normal, .btn-scatter, .btn-reset {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.btn-primary {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.btn-normal {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    color: white;
}

.btn-scatter {
    background: linear-gradient(45deg, #fa709a, #fee140);
    color: white;
}

.btn-reset {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
}

.btn-normal:hover, .btn-scatter:hover, .btn-reset:hover {
    transform: translateY(-2px);
    opacity: 0.9;
}

/* Input buttons */
.input-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

/* History */
.history-stats {
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.stat-item span:first-child {
    color: #b8c5d6;
}

.stat-item span:last-child {
    color: #ffd700;
    font-weight: bold;
}

.recent-history h3 {
    margin-bottom: 10px;
    color: #e0e6ed;
    font-size: 1rem;
}

.history-list {
    max-height: 200px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
}

.history-item {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
}

.history-item:last-child {
    border-bottom: none;
}

.history-item.scatter {
    color: #ffd700;
    font-weight: bold;
}

.history-item.normal {
    color: #b8c5d6;
}

/* Responsive */
@media (max-width: 480px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 1.8rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .prediction-value {
        font-size: 2rem;
    }
    
    .input-buttons {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.7);
}
