class SlotScatterPredictor {
    constructor() {
        this.data = this.loadData();
        this.initializeElements();
        this.bindEvents();
        this.updateDisplay();
    }

    initializeElements() {
        // Settings
        this.scatterRateInput = document.getElementById('scatterRate');
        this.minScatterInput = document.getElementById('minScatter');
        
        // Display elements
        this.currentStreakEl = document.getElementById('currentStreak');
        this.totalSpinsEl = document.getElementById('totalSpins');
        this.totalScattersEl = document.getElementById('totalScatters');
        this.actualRateEl = document.getElementById('actualRate');
        this.predictedSpinsEl = document.getElementById('predictedSpins');
        this.prob10El = document.getElementById('prob10');
        this.prob20El = document.getElementById('prob20');
        this.prob50El = document.getElementById('prob50');
        this.longestStreakEl = document.getElementById('longestStreak');
        this.avgSpinsPerScatterEl = document.getElementById('avgSpinsPerScatter');
        this.recentHistoryEl = document.getElementById('recentHistory');
        
        // Buttons
        this.calculateBtn = document.getElementById('calculateBtn');
        this.normalSpinBtn = document.getElementById('normalSpinBtn');
        this.scatterSpinBtn = document.getElementById('scatterSpinBtn');
        this.resetBtn = document.getElementById('resetBtn');
    }

    bindEvents() {
        this.calculateBtn.addEventListener('click', () => this.calculatePrediction());
        this.normalSpinBtn.addEventListener('click', () => this.recordSpin(false));
        this.scatterSpinBtn.addEventListener('click', () => this.recordSpin(true));
        this.resetBtn.addEventListener('click', () => this.resetData());
        
        // Auto-save settings
        this.scatterRateInput.addEventListener('change', () => this.saveData());
        this.minScatterInput.addEventListener('change', () => this.saveData());
    }

    loadData() {
        const defaultData = {
            currentStreak: 0,
            totalSpins: 0,
            totalScatters: 0,
            longestStreak: 0,
            history: [],
            scatterRate: 3,
            minScatter: 3
        };
        
        const saved = localStorage.getItem('slotPredictorData');
        if (saved) {
            return { ...defaultData, ...JSON.parse(saved) };
        }
        return defaultData;
    }

    saveData() {
        this.data.scatterRate = parseFloat(this.scatterRateInput.value);
        this.data.minScatter = parseInt(this.minScatterInput.value);
        localStorage.setItem('slotPredictorData', JSON.stringify(this.data));
    }

    recordSpin(isScatter) {
        this.data.totalSpins++;
        
        if (isScatter) {
            this.data.totalScatters++;
            this.data.history.push({
                type: 'scatter',
                streak: this.data.currentStreak,
                timestamp: new Date().toLocaleTimeString()
            });
            
            if (this.data.currentStreak > this.data.longestStreak) {
                this.data.longestStreak = this.data.currentStreak;
            }
            
            this.data.currentStreak = 0;
        } else {
            this.data.currentStreak++;
            this.data.history.push({
                type: 'normal',
                streak: this.data.currentStreak,
                timestamp: new Date().toLocaleTimeString()
            });
        }
        
        // Keep only last 50 records
        if (this.data.history.length > 50) {
            this.data.history = this.data.history.slice(-50);
        }
        
        this.saveData();
        this.updateDisplay();
        
        // Auto-calculate after each spin
        this.calculatePrediction();
    }

    calculatePrediction() {
        const scatterRate = this.data.scatterRate / 100;
        const currentStreak = this.data.currentStreak;
        
        // Basic probability calculation
        const baseExpectedSpins = Math.round(1 / scatterRate);
        
        // Adjust based on current streak (gambler's fallacy consideration)
        let adjustedPrediction = baseExpectedSpins;
        
        if (currentStreak > baseExpectedSpins) {
            // If streak is longer than expected, slightly reduce prediction
            adjustedPrediction = Math.max(1, baseExpectedSpins - Math.floor(currentStreak * 0.1));
        } else if (currentStreak < baseExpectedSpins * 0.5) {
            // If streak is much shorter, slightly increase prediction
            adjustedPrediction = baseExpectedSpins + Math.floor((baseExpectedSpins - currentStreak) * 0.2);
        }
        
        // Calculate probabilities for different spin ranges
        const prob10 = this.calculateProbability(scatterRate, 10);
        const prob20 = this.calculateProbability(scatterRate, 20);
        const prob50 = this.calculateProbability(scatterRate, 50);
        
        // Update display
        this.predictedSpinsEl.textContent = adjustedPrediction;
        this.prob10El.textContent = `${(prob10 * 100).toFixed(1)}%`;
        this.prob20El.textContent = `${(prob20 * 100).toFixed(1)}%`;
        this.prob50El.textContent = `${(prob50 * 100).toFixed(1)}%`;
    }

    calculateProbability(rate, spins) {
        // Probability of getting at least one scatter in 'spins' attempts
        return 1 - Math.pow(1 - rate, spins);
    }

    updateDisplay() {
        // Load settings
        this.scatterRateInput.value = this.data.scatterRate;
        this.minScatterInput.value = this.data.minScatter;
        
        // Update stats
        this.currentStreakEl.textContent = this.data.currentStreak;
        this.totalSpinsEl.textContent = this.data.totalSpins;
        this.totalScattersEl.textContent = this.data.totalScatters;
        this.longestStreakEl.textContent = this.data.longestStreak;
        
        // Calculate actual rate
        const actualRate = this.data.totalSpins > 0 
            ? (this.data.totalScatters / this.data.totalSpins * 100).toFixed(2)
            : 0;
        this.actualRateEl.textContent = `${actualRate}%`;
        
        // Calculate average spins per scatter
        const avgSpins = this.data.totalScatters > 0 
            ? (this.data.totalSpins / this.data.totalScatters).toFixed(1)
            : 0;
        this.avgSpinsPerScatterEl.textContent = avgSpins;
        
        // Update history
        this.updateHistory();
    }

    updateHistory() {
        if (this.data.history.length === 0) {
            this.recentHistoryEl.innerHTML = '<p>Chưa có dữ liệu</p>';
            return;
        }
        
        const recent = this.data.history.slice(-10).reverse();
        const historyHTML = recent.map(item => {
            const className = item.type === 'scatter' ? 'scatter' : 'normal';
            const icon = item.type === 'scatter' ? '⭐' : '🎰';
            const text = item.type === 'scatter' 
                ? `Scatter sau ${item.streak} spin`
                : `Spin thường (${item.streak})`;
            
            return `<div class="history-item ${className}">
                ${icon} ${text} - ${item.timestamp}
            </div>`;
        }).join('');
        
        this.recentHistoryEl.innerHTML = historyHTML;
    }

    resetData() {
        if (confirm('Bạn có chắc muốn xóa tất cả dữ liệu?')) {
            this.data = {
                currentStreak: 0,
                totalSpins: 0,
                totalScatters: 0,
                longestStreak: 0,
                history: [],
                scatterRate: this.data.scatterRate,
                minScatter: this.data.minScatter
            };
            this.saveData();
            this.updateDisplay();
            this.predictedSpinsEl.textContent = '-';
            this.prob10El.textContent = '-';
            this.prob20El.textContent = '-';
            this.prob50El.textContent = '-';
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SlotScatterPredictor();
});

// Service Worker registration for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('service-worker.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
