class SlotPredictorUI {
    constructor() {
        this.currentTab = 'dashboard';
        this.isMonitoring = false;
        this.scrapingActive = false;
        this.stats = {
            totalSpins: 0,
            totalScatters: 0,
            currentStreak: 0,
            scatterRate: 0
        };
        
        this.initializeUI();
        this.setupEventListeners();
        this.loadInitialData();
    }

    initializeUI() {
        // Setup tab navigation
        this.setupTabNavigation();
        
        // Initialize components
        this.updateStats();
        this.updateConnectionStatus(false);
        
        // Setup form handlers
        this.setupFormHandlers();
        
        console.log('UI initialized successfully');
    }

    setupTabNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const tabContents = document.querySelectorAll('.tab-content');

        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const tabId = item.dataset.tab;
                
                // Update active nav item
                navItems.forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
                
                // Update active tab content
                tabContents.forEach(tab => tab.classList.remove('active'));
                document.getElementById(tabId).classList.add('active');
                
                this.currentTab = tabId;
                this.onTabChange(tabId);
            });
        });
    }

    setupEventListeners() {
        // Monitor controls
        document.getElementById('startMonitorBtn').addEventListener('click', () => {
            this.startMonitoring();
        });

        document.getElementById('stopMonitorBtn').addEventListener('click', () => {
            this.stopMonitoring();
        });

        // Scraping controls
        document.getElementById('stopScrapingBtn').addEventListener('click', () => {
            this.stopScraping();
        });

        document.getElementById('testSelectorsBtn').addEventListener('click', () => {
            this.testSelectors();
        });

        // Prediction controls
        document.getElementById('updatePredictionBtn').addEventListener('click', () => {
            this.updatePrediction();
        });

        document.getElementById('confidenceThreshold').addEventListener('input', (e) => {
            document.getElementById('confidenceValue').textContent = e.target.value + '%';
        });

        // Data management
        document.getElementById('exportDataBtn').addEventListener('click', () => {
            this.exportData();
        });

        document.getElementById('importDataBtn').addEventListener('click', () => {
            this.importData();
        });

        document.getElementById('clearDataBtn').addEventListener('click', () => {
            this.clearData();
        });

        // Electron API event listeners
        if (window.electronAPI) {
            window.electronAPI.onScrapingUpdate((event, data) => {
                this.handleScrapingUpdate(data);
            });

            window.electronAPI.onPredictionUpdate((event, data) => {
                this.handlePredictionUpdate(data);
            });

            window.electronAPI.onNewSession(() => {
                this.resetSession();
            });

            window.electronAPI.onStartMonitoring(() => {
                this.startMonitoring();
            });

            window.electronAPI.onStopMonitoring(() => {
                this.stopMonitoring();
            });

            window.electronAPI.onDataCleared(() => {
                this.resetStats();
            });
        }
    }

    setupFormHandlers() {
        // Scraping form
        const scrapingForm = document.getElementById('scrapingForm');
        scrapingForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.startScraping();
        });
    }

    async loadInitialData() {
        try {
            if (window.electronAPI) {
                const stats = await window.electronAPI.getStatistics();
                this.updateStatsFromData(stats);
                
                const history = await window.electronAPI.getHistory(10);
                this.updateActivityList(history);
            }
        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.showError('Failed to load initial data');
        }
    }

    async startMonitoring() {
        try {
            this.isMonitoring = true;
            this.updateMonitorControls();
            this.updateConnectionStatus(true);
            
            this.addLogEntry('Monitoring started', 'success');
            this.updateMonitorStatus('Monitoring active - Waiting for game data...');
            
            // Start the actual monitoring process
            if (window.electronAPI) {
                const config = this.getScrapingConfig();
                await window.electronAPI.startScraping(config);
            }
            
        } catch (error) {
            console.error('Failed to start monitoring:', error);
            this.showError('Failed to start monitoring: ' + error.message);
            this.stopMonitoring();
        }
    }

    async stopMonitoring() {
        try {
            this.isMonitoring = false;
            this.scrapingActive = false;
            this.updateMonitorControls();
            this.updateConnectionStatus(false);
            
            this.addLogEntry('Monitoring stopped', 'warning');
            this.updateMonitorStatus('Monitoring stopped');
            
            if (window.electronAPI) {
                await window.electronAPI.stopScraping();
            }
            
        } catch (error) {
            console.error('Failed to stop monitoring:', error);
            this.showError('Failed to stop monitoring: ' + error.message);
        }
    }

    async startScraping() {
        try {
            const config = this.getScrapingConfig();
            
            if (!config.targetUrl) {
                this.showError('Please enter a target URL');
                return;
            }

            this.scrapingActive = true;
            this.addLogEntry(`Starting scraping for: ${config.targetUrl}`, 'success');
            
            if (window.electronAPI) {
                const result = await window.electronAPI.startScraping(config);
                if (result.success) {
                    this.addLogEntry('Scraping engine started successfully', 'success');
                } else {
                    throw new Error(result.error);
                }
            }
            
        } catch (error) {
            console.error('Failed to start scraping:', error);
            this.addLogEntry(`Scraping failed: ${error.message}`, 'error');
            this.scrapingActive = false;
        }
    }

    async stopScraping() {
        try {
            this.scrapingActive = false;
            this.addLogEntry('Stopping scraping engine...', 'warning');
            
            if (window.electronAPI) {
                await window.electronAPI.stopScraping();
            }
            
            this.addLogEntry('Scraping stopped', 'warning');
        } catch (error) {
            console.error('Failed to stop scraping:', error);
            this.addLogEntry(`Failed to stop scraping: ${error.message}`, 'error');
        }
    }

    async testSelectors() {
        const selectors = {
            spinResult: document.getElementById('spinResultSelector').value,
            scatter: document.getElementById('scatterSelector').value,
            balance: document.getElementById('balanceSelector').value
        };

        this.addLogEntry('Testing selectors...', 'warning');
        this.addLogEntry(`Spin Result: ${selectors.spinResult}`, 'info');
        this.addLogEntry(`Scatter: ${selectors.scatter}`, 'info');
        this.addLogEntry(`Balance: ${selectors.balance}`, 'info');
        
        // TODO: Implement actual selector testing
        setTimeout(() => {
            this.addLogEntry('Selector test completed', 'success');
        }, 1000);
    }

    async updatePrediction() {
        try {
            const settings = {
                model: document.getElementById('predictionModel').value,
                confidenceThreshold: parseInt(document.getElementById('confidenceThreshold').value),
                lookbackPeriod: parseInt(document.getElementById('lookbackPeriod').value)
            };

            if (window.electronAPI) {
                await window.electronAPI.updateSettings(settings);
                const prediction = await window.electronAPI.getPrediction(this.stats);
                this.updatePredictionDisplay(prediction);
            }
            
        } catch (error) {
            console.error('Failed to update prediction:', error);
            this.showError('Failed to update prediction: ' + error.message);
        }
    }

    getScrapingConfig() {
        return {
            targetUrl: document.getElementById('targetUrl').value,
            interval: parseInt(document.getElementById('scrapingInterval').value),
            maxRetries: parseInt(document.getElementById('maxRetries').value),
            userAgent: document.getElementById('userAgent').value,
            stealthMode: document.getElementById('stealthMode').checked,
            selectors: {
                spinResult: document.getElementById('spinResultSelector').value,
                scatter: document.getElementById('scatterSelector').value,
                balance: document.getElementById('balanceSelector').value
            }
        };
    }

    updateStats() {
        document.getElementById('totalSpins').textContent = this.stats.totalSpins;
        document.getElementById('totalScatters').textContent = this.stats.totalScatters;
        document.getElementById('currentStreak').textContent = this.stats.currentStreak;
        document.getElementById('scatterRate').textContent = this.stats.scatterRate.toFixed(2) + '%';
    }

    updateStatsFromData(data) {
        if (data) {
            this.stats = { ...this.stats, ...data };
            this.updateStats();
        }
    }

    updateConnectionStatus(connected) {
        const statusEl = document.getElementById('connectionStatus');
        const icon = statusEl.querySelector('i');
        const text = statusEl.querySelector('span');
        
        if (connected) {
            statusEl.className = 'status-indicator connected';
            text.textContent = 'Connected';
        } else {
            statusEl.className = 'status-indicator disconnected';
            text.textContent = 'Disconnected';
        }
    }

    updateMonitorControls() {
        const startBtn = document.getElementById('startMonitorBtn');
        const stopBtn = document.getElementById('stopMonitorBtn');
        
        startBtn.disabled = this.isMonitoring;
        stopBtn.disabled = !this.isMonitoring;
    }

    updateMonitorStatus(message) {
        document.getElementById('monitorStatus').innerHTML = `<p>${message}</p>`;
    }

    updatePredictionDisplay(prediction) {
        if (prediction) {
            document.getElementById('predictedSpins').textContent = prediction.spins || '-';
            document.getElementById('confidenceBar').style.width = (prediction.confidence || 0) + '%';
            document.getElementById('confidenceText').textContent = (prediction.confidence || 0) + '%';
            
            // Update probability values
            if (prediction.probabilities) {
                document.getElementById('prob5').textContent = (prediction.probabilities.next5 || 0) + '%';
                document.getElementById('prob10').textContent = (prediction.probabilities.next10 || 0) + '%';
                document.getElementById('prob20').textContent = (prediction.probabilities.next20 || 0) + '%';
                document.getElementById('prob50').textContent = (prediction.probabilities.next50 || 0) + '%';
            }
        }
    }

    addLogEntry(message, type = 'info') {
        const logContainer = document.getElementById('scrapingLog');
        const timestamp = new Date().toLocaleTimeString();
        const entry = document.createElement('p');
        entry.className = `log-entry ${type}`;
        entry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(entry);
        logContainer.scrollTop = logContainer.scrollHeight;
        
        // Keep only last 100 entries
        const entries = logContainer.querySelectorAll('.log-entry');
        if (entries.length > 100) {
            entries[0].remove();
        }
    }

    updateActivityList(history) {
        const activityList = document.getElementById('activityList');
        
        if (!history || history.length === 0) {
            activityList.innerHTML = '<p class="no-data">No activity yet</p>';
            return;
        }
        
        const activityHTML = history.map(item => {
            const time = new Date(item.timestamp).toLocaleTimeString();
            const type = item.type === 'scatter' ? 'scatter' : 'normal';
            const icon = item.type === 'scatter' ? '⭐' : '🎰';
            
            return `<div class="activity-item ${type}">
                ${icon} ${item.description} - ${time}
            </div>`;
        }).join('');
        
        activityList.innerHTML = activityHTML;
    }

    handleScrapingUpdate(data) {
        if (data.type === 'spin') {
            this.stats.totalSpins++;
            this.stats.currentStreak++;
            
            if (data.isScatter) {
                this.stats.totalScatters++;
                this.stats.currentStreak = 0;
                this.addLogEntry('Scatter detected!', 'success');
            } else {
                this.addLogEntry(`Spin ${this.stats.totalSpins} - No scatter`, 'info');
            }
            
            this.stats.scatterRate = this.stats.totalSpins > 0 
                ? (this.stats.totalScatters / this.stats.totalSpins) * 100 
                : 0;
            
            this.updateStats();
        }
        
        if (data.error) {
            this.addLogEntry(`Error: ${data.error}`, 'error');
        }
    }

    handlePredictionUpdate(prediction) {
        this.updatePredictionDisplay(prediction);
        
        if (prediction.insights) {
            document.getElementById('aiInsights').innerHTML = prediction.insights;
        }
    }

    async exportData() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.exportData();
                if (result.success) {
                    this.showSuccess(`Data exported to: ${result.path}`);
                } else {
                    this.showError('Export failed: ' + result.error);
                }
            }
        } catch (error) {
            this.showError('Export failed: ' + error.message);
        }
    }

    async importData() {
        try {
            if (window.electronAPI) {
                const file = await window.electronAPI.selectFile();
                if (!file.canceled) {
                    // TODO: Implement data import
                    this.showSuccess('Data import feature coming soon');
                }
            }
        } catch (error) {
            this.showError('Import failed: ' + error.message);
        }
    }

    async clearData() {
        if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
            try {
                if (window.electronAPI) {
                    await window.electronAPI.clearData();
                    this.resetStats();
                    this.showSuccess('All data cleared successfully');
                }
            } catch (error) {
                this.showError('Failed to clear data: ' + error.message);
            }
        }
    }

    resetStats() {
        this.stats = {
            totalSpins: 0,
            totalScatters: 0,
            currentStreak: 0,
            scatterRate: 0
        };
        this.updateStats();
        this.updateActivityList([]);
    }

    resetSession() {
        this.stopMonitoring();
        this.resetStats();
        this.addLogEntry('New session started', 'success');
    }

    onTabChange(tabId) {
        // Handle tab-specific initialization
        switch (tabId) {
            case 'analytics':
                this.initializeCharts();
                break;
            case 'prediction':
                this.updatePrediction();
                break;
        }
    }

    initializeCharts() {
        // TODO: Initialize Chart.js charts
        console.log('Charts initialization placeholder');
    }

    showError(message) {
        // TODO: Implement proper error notification
        console.error(message);
        alert('Error: ' + message);
    }

    showSuccess(message) {
        // TODO: Implement proper success notification
        console.log(message);
        alert('Success: ' + message);
    }
}

// Initialize the UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SlotPredictorUI();
});
