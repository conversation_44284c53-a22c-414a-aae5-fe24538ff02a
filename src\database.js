const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = this.getDatabasePath();
        this.isInitialized = false;
    }

    getDatabasePath() {
        try {
            const userDataPath = app.getPath('userData');
            return path.join(userDataPath, 'slot-predictor.db');
        } catch (error) {
            // Fallback for development
            return path.join(__dirname, '..', 'data', 'slot-predictor.db');
        }
    }

    async initialize() {
        try {
            // Ensure directory exists
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // Open database
            this.db = new sqlite3.Database(this.dbPath);
            
            // Create tables
            await this.createTables();
            
            this.isInitialized = true;
            console.log('Database initialized successfully');
            
        } catch (error) {
            console.error('Database initialization failed:', error);
            throw error;
        }
    }

    createTables() {
        return new Promise((resolve, reject) => {
            const queries = [
                // Spins table
                `CREATE TABLE IF NOT EXISTS spins (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    type TEXT NOT NULL, -- 'normal' or 'scatter'
                    scatter_count INTEGER DEFAULT 0,
                    balance REAL,
                    session_id TEXT,
                    streak_before INTEGER DEFAULT 0,
                    metadata TEXT -- JSON string for additional data
                )`,
                
                // Sessions table
                `CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    end_time DATETIME,
                    total_spins INTEGER DEFAULT 0,
                    total_scatters INTEGER DEFAULT 0,
                    longest_streak INTEGER DEFAULT 0,
                    target_url TEXT,
                    settings TEXT -- JSON string
                )`,
                
                // Predictions table
                `CREATE TABLE IF NOT EXISTS predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    predicted_spins INTEGER,
                    confidence INTEGER,
                    method TEXT,
                    actual_spins INTEGER,
                    was_accurate BOOLEAN,
                    session_id TEXT,
                    factors TEXT -- JSON string
                )`,
                
                // Settings table
                `CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )`,
                
                // Create indexes
                `CREATE INDEX IF NOT EXISTS idx_spins_timestamp ON spins(timestamp)`,
                `CREATE INDEX IF NOT EXISTS idx_spins_session ON spins(session_id)`,
                `CREATE INDEX IF NOT EXISTS idx_predictions_timestamp ON predictions(timestamp)`,
                `CREATE INDEX IF NOT EXISTS idx_sessions_start_time ON sessions(start_time)`
            ];

            let completed = 0;
            const total = queries.length;

            queries.forEach(query => {
                this.db.run(query, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    completed++;
                    if (completed === total) {
                        resolve();
                    }
                });
            });
        });
    }

    async recordSpin(spinData) {
        return new Promise((resolve, reject) => {
            const {
                type,
                scatterCount = 0,
                balance = null,
                sessionId = null,
                streakBefore = 0,
                metadata = {}
            } = spinData;

            const query = `
                INSERT INTO spins (type, scatter_count, balance, session_id, streak_before, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            `;

            this.db.run(query, [
                type,
                scatterCount,
                balance,
                sessionId,
                streakBefore,
                JSON.stringify(metadata)
            ], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, ...spinData });
                }
            });
        });
    }

    async recordPrediction(predictionData) {
        return new Promise((resolve, reject) => {
            const {
                predictedSpins,
                confidence,
                method,
                sessionId = null,
                factors = {}
            } = predictionData;

            const query = `
                INSERT INTO predictions (predicted_spins, confidence, method, session_id, factors)
                VALUES (?, ?, ?, ?, ?)
            `;

            this.db.run(query, [
                predictedSpins,
                confidence,
                method,
                sessionId,
                JSON.stringify(factors)
            ], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, ...predictionData });
                }
            });
        });
    }

    async updatePredictionAccuracy(predictionId, actualSpins) {
        return new Promise((resolve, reject) => {
            // First get the prediction
            this.db.get(
                'SELECT predicted_spins FROM predictions WHERE id = ?',
                [predictionId],
                (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    if (!row) {
                        reject(new Error('Prediction not found'));
                        return;
                    }

                    // Calculate accuracy
                    const tolerance = Math.max(2, Math.floor(row.predicted_spins * 0.2));
                    const wasAccurate = Math.abs(row.predicted_spins - actualSpins) <= tolerance;

                    // Update the prediction
                    this.db.run(
                        'UPDATE predictions SET actual_spins = ?, was_accurate = ? WHERE id = ?',
                        [actualSpins, wasAccurate, predictionId],
                        (updateErr) => {
                            if (updateErr) {
                                reject(updateErr);
                            } else {
                                resolve({ wasAccurate, actualSpins });
                            }
                        }
                    );
                }
            );
        });
    }

    async getStatistics() {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT 
                    COUNT(*) as total_spins,
                    SUM(CASE WHEN type = 'scatter' THEN 1 ELSE 0 END) as total_scatters,
                    AVG(CASE WHEN type = 'scatter' THEN streak_before ELSE NULL END) as avg_streak,
                    MAX(streak_before) as longest_streak,
                    (SUM(CASE WHEN type = 'scatter' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as scatter_rate
                FROM spins
                WHERE timestamp >= datetime('now', '-7 days')
            `;

            this.db.get(query, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        totalSpins: row.total_spins || 0,
                        totalScatters: row.total_scatters || 0,
                        avgStreak: Math.round(row.avg_streak || 0),
                        longestStreak: row.longest_streak || 0,
                        scatterRate: parseFloat((row.scatter_rate || 0).toFixed(2))
                    });
                }
            });
        });
    }

    async getHistory(limit = 100) {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT 
                    id,
                    timestamp,
                    type,
                    scatter_count,
                    balance,
                    streak_before,
                    metadata
                FROM spins
                ORDER BY timestamp DESC
                LIMIT ?
            `;

            this.db.all(query, [limit], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    const history = rows.map(row => ({
                        id: row.id,
                        timestamp: row.timestamp,
                        type: row.type,
                        scatterCount: row.scatter_count,
                        balance: row.balance,
                        streakBefore: row.streak_before,
                        metadata: row.metadata ? JSON.parse(row.metadata) : {}
                    }));
                    resolve(history);
                }
            });
        });
    }

    async getCurrentStreak() {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT COUNT(*) as streak
                FROM spins
                WHERE id > (
                    SELECT COALESCE(MAX(id), 0)
                    FROM spins
                    WHERE type = 'scatter'
                )
                AND type = 'normal'
            `;

            this.db.get(query, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row.streak || 0);
                }
            });
        });
    }

    async getPredictionAccuracy() {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN was_accurate = 1 THEN 1 ELSE 0 END) as correct,
                    AVG(confidence) as avg_confidence
                FROM predictions
                WHERE actual_spins IS NOT NULL
                AND timestamp >= datetime('now', '-30 days')
            `;

            this.db.get(query, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    const total = row.total || 0;
                    const correct = row.correct || 0;
                    const percentage = total > 0 ? (correct / total) * 100 : 0;

                    resolve({
                        total,
                        correct,
                        percentage: parseFloat(percentage.toFixed(1)),
                        avgConfidence: parseFloat((row.avg_confidence || 0).toFixed(1))
                    });
                }
            });
        });
    }

    async createSession(sessionData) {
        return new Promise((resolve, reject) => {
            const {
                id,
                targetUrl = null,
                settings = {}
            } = sessionData;

            const query = `
                INSERT INTO sessions (id, target_url, settings)
                VALUES (?, ?, ?)
            `;

            this.db.run(query, [
                id,
                targetUrl,
                JSON.stringify(settings)
            ], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id, ...sessionData });
                }
            });
        });
    }

    async updateSession(sessionId, updates) {
        return new Promise((resolve, reject) => {
            const fields = [];
            const values = [];

            Object.keys(updates).forEach(key => {
                if (key === 'settings') {
                    fields.push('settings = ?');
                    values.push(JSON.stringify(updates[key]));
                } else {
                    fields.push(`${key} = ?`);
                    values.push(updates[key]);
                }
            });

            values.push(sessionId);

            const query = `UPDATE sessions SET ${fields.join(', ')} WHERE id = ?`;

            this.db.run(query, values, (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(updates);
                }
            });
        });
    }

    async exportAll() {
        return new Promise((resolve, reject) => {
            const exportData = {};

            // Export spins
            this.db.all('SELECT * FROM spins ORDER BY timestamp', (err, spins) => {
                if (err) {
                    reject(err);
                    return;
                }

                exportData.spins = spins;

                // Export sessions
                this.db.all('SELECT * FROM sessions ORDER BY start_time', (err, sessions) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    exportData.sessions = sessions;

                    // Export predictions
                    this.db.all('SELECT * FROM predictions ORDER BY timestamp', (err, predictions) => {
                        if (err) {
                            reject(err);
                            return;
                        }

                        exportData.predictions = predictions;
                        exportData.exportDate = new Date().toISOString();
                        resolve(exportData);
                    });
                });
            });
        });
    }

    async clearAll() {
        return new Promise((resolve, reject) => {
            const queries = [
                'DELETE FROM spins',
                'DELETE FROM sessions',
                'DELETE FROM predictions'
            ];

            let completed = 0;
            const total = queries.length;

            queries.forEach(query => {
                this.db.run(query, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    completed++;
                    if (completed === total) {
                        resolve();
                    }
                });
            });
        });
    }

    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        reject(err);
                    } else {
                        this.db = null;
                        this.isInitialized = false;
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = { DatabaseManager };
