<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slot Scatter Predictor</title>
    <link rel="stylesheet" href="style.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1a1a2e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎰 Slot Scatter Predictor</h1>
            <p>Dự đoán số lần spin cần thiết để ra scatter</p>
        </header>

        <main>
            <!-- Cài đặt game -->
            <section class="settings-section">
                <h2>⚙️ Cài đặt Game</h2>
                <div class="setting-group">
                    <label for="scatterRate">Tỷ l<PERSON>atter (%):</label>
                    <input type="number" id="scatterRate" value="3" min="0.1" max="50" step="0.1">
                </div>
                <div class="setting-group">
                    <label for="minScatter">Số scatter tối thiểu:</label>
                    <input type="number" id="minScatter" value="3" min="2" max="5">
                </div>
            </section>

            <!-- Thông tin hiện tại -->
            <section class="current-status">
                <h2>📊 Trạng thái hiện tại</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-label">Spin liên tiếp</span>
                        <span class="stat-value" id="currentStreak">0</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-label">Tổng spin</span>
                        <span class="stat-value" id="totalSpins">0</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-label">Scatter đã ra</span>
                        <span class="stat-value" id="totalScatters">0</span>
                    </div>
                    <div class="stat-card">
                        <span class="stat-label">Tỷ lệ thực tế</span>
                        <span class="stat-value" id="actualRate">0%</span>
                    </div>
                </div>
            </section>

            <!-- Dự đoán -->
            <section class="prediction-section">
                <h2>🔮 Dự đoán Scatter</h2>
                <div class="prediction-card">
                    <div class="prediction-main">
                        <span class="prediction-label">Dự đoán số spin cần thiết:</span>
                        <span class="prediction-value" id="predictedSpins">-</span>
                    </div>
                    <div class="prediction-details">
                        <div class="detail-item">
                            <span>Xác suất trong 10 spin tiếp:</span>
                            <span id="prob10">-</span>
                        </div>
                        <div class="detail-item">
                            <span>Xác suất trong 20 spin tiếp:</span>
                            <span id="prob20">-</span>
                        </div>
                        <div class="detail-item">
                            <span>Xác suất trong 50 spin tiếp:</span>
                            <span id="prob50">-</span>
                        </div>
                    </div>
                </div>
                <button id="calculateBtn" class="btn-primary">🎯 Tính toán dự đoán</button>
            </section>

            <!-- Ghi nhận kết quả -->
            <section class="input-section">
                <h2>📝 Ghi nhận kết quả</h2>
                <div class="input-buttons">
                    <button id="normalSpinBtn" class="btn-normal">🎰 Spin thường</button>
                    <button id="scatterSpinBtn" class="btn-scatter">⭐ Ra Scatter</button>
                </div>
                <button id="resetBtn" class="btn-reset">🔄 Reset dữ liệu</button>
            </section>

            <!-- Lịch sử -->
            <section class="history-section">
                <h2>📈 Lịch sử & Thống kê</h2>
                <div class="history-stats">
                    <div class="stat-item">
                        <span>Streak dài nhất:</span>
                        <span id="longestStreak">0</span>
                    </div>
                    <div class="stat-item">
                        <span>Trung bình spin/scatter:</span>
                        <span id="avgSpinsPerScatter">0</span>
                    </div>
                </div>
                <div class="recent-history">
                    <h3>10 lần gần nhất:</h3>
                    <div id="recentHistory" class="history-list">
                        <p>Chưa có dữ liệu</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
