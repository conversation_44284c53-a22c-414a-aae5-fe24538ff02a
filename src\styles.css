/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow: hidden;
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 25px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-left h1 {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.version {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-indicator.connected {
    color: #4ade80;
}

.status-indicator.disconnected {
    color: #f87171;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: rgba(255, 255, 255, 0.03);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 25px;
    background: none;
    border: none;
    color: #b8c5d6;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
}

.nav-item.active {
    background: linear-gradient(90deg, rgba(255, 215, 0, 0.2), transparent);
    color: #ffd700;
    border-right: 3px solid #ffd700;
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.card h3 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

/* Stats Card */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 5px;
}

.stat-label {
    color: #b8c5d6;
    font-size: 0.9rem;
}

/* Prediction Card */
.prediction-display {
    text-align: center;
}

.prediction-main {
    margin-bottom: 20px;
}

.prediction-value {
    display: block;
    font-size: 3rem;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.prediction-label {
    color: #b8c5d6;
    font-size: 1rem;
}

.confidence-bar {
    position: relative;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffd700, #ffed4e);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.confidence-text {
    color: #ffd700;
    font-weight: bold;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
}

.btn-danger {
    background: linear-gradient(45deg, #ef4444, #dc2626);
    color: #ffffff;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(239, 68, 68, 0.4);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #e0e6ed;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    font-size: 0.95rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.form-group input[type="range"] {
    margin-right: 10px;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

/* Monitor Controls */
.monitor-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.monitor-status {
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border-left: 4px solid #ffd700;
}

/* Activity List */
.activity-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.no-data {
    text-align: center;
    color: #b8c5d6;
    font-style: italic;
    padding: 20px;
}

/* Log Container */
.log-container {
    height: 300px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
}

.log-entry {
    margin-bottom: 5px;
    color: #b8c5d6;
}

.log-entry.success {
    color: #4ade80;
}

.log-entry.error {
    color: #f87171;
}

.log-entry.warning {
    color: #fbbf24;
}

/* Probability Grid */
.probability-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.prob-item {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
}

.prob-label {
    color: #b8c5d6;
}

.prob-value {
    color: #ffd700;
    font-weight: bold;
}

/* Insights */
.insights-container {
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    min-height: 150px;
}

/* Charts */
.chart-container {
    height: 400px;
    padding: 20px;
}

/* Tables */
.stats-table-container {
    overflow-x: auto;
}

.stats-table {
    width: 100%;
    border-collapse: collapse;
}

.stats-table th,
.stats-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-table th {
    color: #ffd700;
    font-weight: 600;
}

.stats-table td {
    color: #e0e6ed;
}

/* Settings */
.settings-container .card {
    max-width: 600px;
}

.data-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.about-info {
    color: #b8c5d6;
    line-height: 1.6;
}

.about-info p {
    margin-bottom: 8px;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.5);
}

/* Responsive */
@media (max-width: 1200px) {
    .sidebar {
        width: 200px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .nav-menu {
        flex-direction: row;
        overflow-x: auto;
    }
    
    .content-area {
        padding: 15px;
    }
}
