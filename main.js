const { app, BrowserWindow, ipcMain, dialog, Menu, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { ScrapingEngine } = require('./src/scraper');
const { PredictionEngine } = require('./src/predictor');
const { DatabaseManager } = require('./src/database');

class SlotPredictorApp {
    constructor() {
        this.mainWindow = null;
        this.scrapingEngine = null;
        this.predictionEngine = null;
        this.database = null;
        this.isDevMode = process.argv.includes('--dev');
        
        this.initializeApp();
    }

    initializeApp() {
        app.whenReady().then(() => {
            this.createMainWindow();
            this.setupMenu();
            this.initializeEngines();
            this.setupIpcHandlers();
        });

        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });
    }

    createMainWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            icon: path.join(__dirname, 'assets', 'icon.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js'),
                webSecurity: false // Needed for cross-origin requests
            },
            titleBarStyle: 'default',
            show: false
        });

        this.mainWindow.loadFile('src/index.html');

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            if (this.isDevMode) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
            this.cleanup();
        });
    }

    setupMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'New Session',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => this.newSession()
                    },
                    {
                        label: 'Export Data',
                        accelerator: 'CmdOrCtrl+E',
                        click: () => this.exportData()
                    },
                    { type: 'separator' },
                    {
                        label: 'Exit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => app.quit()
                    }
                ]
            },
            {
                label: 'Tools',
                submenu: [
                    {
                        label: 'Start Monitoring',
                        accelerator: 'CmdOrCtrl+M',
                        click: () => this.startMonitoring()
                    },
                    {
                        label: 'Stop Monitoring',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => this.stopMonitoring()
                    },
                    { type: 'separator' },
                    {
                        label: 'Clear Data',
                        click: () => this.clearData()
                    }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'About',
                        click: () => this.showAbout()
                    },
                    {
                        label: 'Documentation',
                        click: () => shell.openExternal('https://github.com/your-repo/docs')
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    async initializeEngines() {
        try {
            // Initialize database
            this.database = new DatabaseManager();
            await this.database.initialize();

            // Initialize prediction engine
            this.predictionEngine = new PredictionEngine(this.database);

            // Initialize scraping engine
            this.scrapingEngine = new ScrapingEngine({
                headless: !this.isDevMode,
                database: this.database,
                predictionEngine: this.predictionEngine
            });

            console.log('All engines initialized successfully');
        } catch (error) {
            console.error('Failed to initialize engines:', error);
            this.showError('Initialization Error', error.message);
        }
    }

    setupIpcHandlers() {
        // Scraping controls
        ipcMain.handle('start-scraping', async (event, config) => {
            try {
                return await this.scrapingEngine.start(config);
            } catch (error) {
                throw error;
            }
        });

        ipcMain.handle('stop-scraping', async () => {
            return await this.scrapingEngine.stop();
        });

        ipcMain.handle('get-scraping-status', () => {
            return this.scrapingEngine.getStatus();
        });

        // Prediction controls
        ipcMain.handle('get-prediction', async (event, data) => {
            return await this.predictionEngine.predict(data);
        });

        ipcMain.handle('update-settings', async (event, settings) => {
            return await this.predictionEngine.updateSettings(settings);
        });

        // Data management
        ipcMain.handle('get-statistics', async () => {
            return await this.database.getStatistics();
        });

        ipcMain.handle('get-history', async (event, limit = 100) => {
            return await this.database.getHistory(limit);
        });

        ipcMain.handle('export-data', async () => {
            return await this.exportData();
        });

        ipcMain.handle('clear-data', async () => {
            return await this.clearData();
        });

        // File operations
        ipcMain.handle('select-file', async () => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                properties: ['openFile'],
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });
            return result;
        });
    }

    async newSession() {
        if (this.scrapingEngine) {
            await this.scrapingEngine.stop();
        }
        this.mainWindow.webContents.send('new-session');
    }

    async startMonitoring() {
        this.mainWindow.webContents.send('start-monitoring');
    }

    async stopMonitoring() {
        this.mainWindow.webContents.send('stop-monitoring');
    }

    async exportData() {
        try {
            const result = await dialog.showSaveDialog(this.mainWindow, {
                defaultPath: `slot-data-${new Date().toISOString().split('T')[0]}.json`,
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!result.canceled) {
                const data = await this.database.exportAll();
                fs.writeFileSync(result.filePath, JSON.stringify(data, null, 2));
                return { success: true, path: result.filePath };
            }
        } catch (error) {
            this.showError('Export Error', error.message);
            return { success: false, error: error.message };
        }
    }

    async clearData() {
        const result = await dialog.showMessageBox(this.mainWindow, {
            type: 'warning',
            buttons: ['Cancel', 'Clear All Data'],
            defaultId: 0,
            title: 'Clear Data',
            message: 'Are you sure you want to clear all data?',
            detail: 'This action cannot be undone.'
        });

        if (result.response === 1) {
            await this.database.clearAll();
            this.mainWindow.webContents.send('data-cleared');
            return true;
        }
        return false;
    }

    showAbout() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'About Slot Scatter Predictor Pro',
            message: 'Slot Scatter Predictor Pro v1.0.0',
            detail: 'Advanced slot game analysis and prediction tool.\n\nFor educational and research purposes only.'
        });
    }

    showError(title, message) {
        dialog.showErrorBox(title, message);
    }

    async cleanup() {
        if (this.scrapingEngine) {
            await this.scrapingEngine.cleanup();
        }
        if (this.database) {
            await this.database.close();
        }
    }
}

// Create app instance
new SlotPredictorApp();
