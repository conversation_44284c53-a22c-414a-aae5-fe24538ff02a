const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // Scraping controls
    startScraping: (config) => ipcRenderer.invoke('start-scraping', config),
    stopScraping: () => ipcRenderer.invoke('stop-scraping'),
    getScrapingStatus: () => ipcRenderer.invoke('get-scraping-status'),

    // Prediction controls
    getPrediction: (data) => ipcRenderer.invoke('get-prediction', data),
    updateSettings: (settings) => ipcRenderer.invoke('update-settings', settings),

    // Data management
    getStatistics: () => ipcRenderer.invoke('get-statistics'),
    getHistory: (limit) => ipcRenderer.invoke('get-history', limit),
    exportData: () => ipcRenderer.invoke('export-data'),
    clearData: () => ipcRenderer.invoke('clear-data'),

    // File operations
    selectFile: () => ipcRenderer.invoke('select-file'),

    // Event listeners
    onScrapingUpdate: (callback) => {
        ipcRenderer.on('scraping-update', callback);
        return () => ipcRenderer.removeListener('scraping-update', callback);
    },

    onPredictionUpdate: (callback) => {
        ipcRenderer.on('prediction-update', callback);
        return () => ipcRenderer.removeListener('prediction-update', callback);
    },

    onNewSession: (callback) => {
        ipcRenderer.on('new-session', callback);
        return () => ipcRenderer.removeListener('new-session', callback);
    },

    onStartMonitoring: (callback) => {
        ipcRenderer.on('start-monitoring', callback);
        return () => ipcRenderer.removeListener('start-monitoring', callback);
    },

    onStopMonitoring: (callback) => {
        ipcRenderer.on('stop-monitoring', callback);
        return () => ipcRenderer.removeListener('stop-monitoring', callback);
    },

    onDataCleared: (callback) => {
        ipcRenderer.on('data-cleared', callback);
        return () => ipcRenderer.removeListener('data-cleared', callback);
    },

    // Utility functions
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    showNotification: (title, body) => ipcRenderer.invoke('show-notification', { title, body }),

    // Development helpers
    isDev: () => process.argv.includes('--dev'),
    getVersion: () => process.env.npm_package_version || '1.0.0'
});
