{"name": "slot-scatter-predictor-pro", "version": "1.0.0", "description": "Advanced slot game scatter prediction tool with web scraping capabilities", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-all": "electron-builder --win --mac --linux", "pack": "electron-builder --dir", "dist": "npm run build", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.slotpredictor.app", "productName": "<PERSON><PERSON> Scatter Predictor Pro", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "keywords": ["slot", "predictor", "gambling", "automation", "scraping"], "author": "Slot Predictor Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"puppeteer": "^21.5.0", "cheerio": "^1.0.0-rc.12", "axios": "^1.6.0", "ws": "^8.14.2", "node-cron": "^3.0.3", "sqlite3": "^5.1.6", "crypto-js": "^4.2.0", "user-agents": "^1.1.0", "proxy-agent": "^6.3.1"}, "engines": {"node": ">=16.0.0"}}