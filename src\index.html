<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slot Scatter Predictor Pro</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <h1><i class="fas fa-dice"></i> Slot Scatter Predictor Pro</h1>
                <span class="version">v1.0.0</span>
            </div>
            <div class="header-right">
                <div class="status-indicator" id="connectionStatus">
                    <i class="fas fa-circle"></i>
                    <span>Disconnected</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <nav class="nav-menu">
                    <button class="nav-item active" data-tab="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </button>
                    <button class="nav-item" data-tab="scraping">
                        <i class="fas fa-spider"></i>
                        Web Scraping
                    </button>
                    <button class="nav-item" data-tab="prediction">
                        <i class="fas fa-crystal-ball"></i>
                        Prediction
                    </button>
                    <button class="nav-item" data-tab="analytics">
                        <i class="fas fa-chart-line"></i>
                        Analytics
                    </button>
                    <button class="nav-item" data-tab="settings">
                        <i class="fas fa-cog"></i>
                        Settings
                    </button>
                </nav>
            </aside>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Dashboard Tab -->
                <div class="tab-content active" id="dashboard">
                    <div class="dashboard-grid">
                        <!-- Quick Stats -->
                        <div class="card stats-card">
                            <h3><i class="fas fa-chart-bar"></i> Quick Stats</h3>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-value" id="totalSpins">0</span>
                                    <span class="stat-label">Total Spins</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value" id="totalScatters">0</span>
                                    <span class="stat-label">Scatters</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value" id="currentStreak">0</span>
                                    <span class="stat-label">Current Streak</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-value" id="scatterRate">0%</span>
                                    <span class="stat-label">Scatter Rate</span>
                                </div>
                            </div>
                        </div>

                        <!-- Current Prediction -->
                        <div class="card prediction-card">
                            <h3><i class="fas fa-magic"></i> Current Prediction</h3>
                            <div class="prediction-display">
                                <div class="prediction-main">
                                    <span class="prediction-value" id="predictedSpins">-</span>
                                    <span class="prediction-label">Spins to Scatter</span>
                                </div>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" id="confidenceBar"></div>
                                    <span class="confidence-text" id="confidenceText">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Live Monitor -->
                        <div class="card monitor-card">
                            <h3><i class="fas fa-eye"></i> Live Monitor</h3>
                            <div class="monitor-controls">
                                <button class="btn btn-primary" id="startMonitorBtn">
                                    <i class="fas fa-play"></i> Start Monitoring
                                </button>
                                <button class="btn btn-secondary" id="stopMonitorBtn" disabled>
                                    <i class="fas fa-stop"></i> Stop
                                </button>
                            </div>
                            <div class="monitor-status" id="monitorStatus">
                                <p>Ready to monitor</p>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="card activity-card">
                            <h3><i class="fas fa-history"></i> Recent Activity</h3>
                            <div class="activity-list" id="activityList">
                                <p class="no-data">No activity yet</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Web Scraping Tab -->
                <div class="tab-content" id="scraping">
                    <div class="scraping-container">
                        <div class="card">
                            <h3><i class="fas fa-globe"></i> Website Configuration</h3>
                            <form class="scraping-form" id="scrapingForm">
                                <div class="form-group">
                                    <label for="targetUrl">Target URL:</label>
                                    <input type="url" id="targetUrl" placeholder="https://example-casino.com/slots" required>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="scrapingInterval">Interval (ms):</label>
                                        <input type="number" id="scrapingInterval" value="1000" min="500" max="10000">
                                    </div>
                                    <div class="form-group">
                                        <label for="maxRetries">Max Retries:</label>
                                        <input type="number" id="maxRetries" value="3" min="1" max="10">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="userAgent">User Agent:</label>
                                    <select id="userAgent">
                                        <option value="chrome">Chrome (Latest)</option>
                                        <option value="firefox">Firefox (Latest)</option>
                                        <option value="safari">Safari (Latest)</option>
                                        <option value="random">Random</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="stealthMode" checked>
                                        Enable Stealth Mode
                                    </label>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-rocket"></i> Start Scraping
                                    </button>
                                    <button type="button" class="btn btn-danger" id="stopScrapingBtn">
                                        <i class="fas fa-stop"></i> Stop Scraping
                                    </button>
                                </div>
                            </form>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-code"></i> Element Selectors</h3>
                            <div class="selectors-form">
                                <div class="form-group">
                                    <label for="spinResultSelector">Spin Result Selector:</label>
                                    <input type="text" id="spinResultSelector" placeholder=".spin-result, #result" value=".game-result">
                                </div>
                                <div class="form-group">
                                    <label for="scatterSelector">Scatter Selector:</label>
                                    <input type="text" id="scatterSelector" placeholder=".scatter, .bonus" value=".scatter-symbol">
                                </div>
                                <div class="form-group">
                                    <label for="balanceSelector">Balance Selector:</label>
                                    <input type="text" id="balanceSelector" placeholder=".balance, #user-balance" value=".user-balance">
                                </div>
                                <button class="btn btn-secondary" id="testSelectorsBtn">
                                    <i class="fas fa-vial"></i> Test Selectors
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-terminal"></i> Scraping Log</h3>
                            <div class="log-container" id="scrapingLog">
                                <p class="log-entry">Ready to start scraping...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Prediction Tab -->
                <div class="tab-content" id="prediction">
                    <div class="prediction-container">
                        <div class="card">
                            <h3><i class="fas fa-brain"></i> Prediction Engine</h3>
                            <div class="prediction-settings">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="predictionModel">Model:</label>
                                        <select id="predictionModel">
                                            <option value="statistical">Statistical</option>
                                            <option value="pattern">Pattern Recognition</option>
                                            <option value="ml">Machine Learning</option>
                                            <option value="hybrid">Hybrid</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="confidenceThreshold">Confidence Threshold:</label>
                                        <input type="range" id="confidenceThreshold" min="0" max="100" value="70">
                                        <span id="confidenceValue">70%</span>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="lookbackPeriod">Lookback Period (spins):</label>
                                    <input type="number" id="lookbackPeriod" value="100" min="10" max="1000">
                                </div>

                                <button class="btn btn-primary" id="updatePredictionBtn">
                                    <i class="fas fa-sync"></i> Update Prediction
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-chart-pie"></i> Probability Analysis</h3>
                            <div class="probability-grid">
                                <div class="prob-item">
                                    <span class="prob-label">Next 5 spins:</span>
                                    <span class="prob-value" id="prob5">0%</span>
                                </div>
                                <div class="prob-item">
                                    <span class="prob-label">Next 10 spins:</span>
                                    <span class="prob-value" id="prob10">0%</span>
                                </div>
                                <div class="prob-item">
                                    <span class="prob-label">Next 20 spins:</span>
                                    <span class="prob-value" id="prob20">0%</span>
                                </div>
                                <div class="prob-item">
                                    <span class="prob-label">Next 50 spins:</span>
                                    <span class="prob-value" id="prob50">0%</span>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-lightbulb"></i> AI Insights</h3>
                            <div class="insights-container" id="aiInsights">
                                <p>No insights available yet. Start monitoring to generate AI insights.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div class="tab-content" id="analytics">
                    <div class="analytics-container">
                        <div class="card">
                            <h3><i class="fas fa-chart-area"></i> Performance Charts</h3>
                            <div class="chart-container">
                                <canvas id="performanceChart"></canvas>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-table"></i> Detailed Statistics</h3>
                            <div class="stats-table-container">
                                <table class="stats-table" id="detailedStats">
                                    <thead>
                                        <tr>
                                            <th>Metric</th>
                                            <th>Value</th>
                                            <th>Trend</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Average Spins per Scatter</td>
                                            <td id="avgSpinsPerScatter">-</td>
                                            <td><i class="fas fa-minus"></i></td>
                                        </tr>
                                        <tr>
                                            <td>Longest Streak</td>
                                            <td id="longestStreak">-</td>
                                            <td><i class="fas fa-minus"></i></td>
                                        </tr>
                                        <tr>
                                            <td>Prediction Accuracy</td>
                                            <td id="predictionAccuracy">-</td>
                                            <td><i class="fas fa-minus"></i></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-content" id="settings">
                    <div class="settings-container">
                        <div class="card">
                            <h3><i class="fas fa-sliders-h"></i> General Settings</h3>
                            <div class="settings-form">
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="autoStart" checked>
                                        Auto-start monitoring on app launch
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="notifications" checked>
                                        Enable notifications
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="soundAlerts">
                                        Sound alerts for predictions
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-database"></i> Data Management</h3>
                            <div class="data-actions">
                                <button class="btn btn-secondary" id="exportDataBtn">
                                    <i class="fas fa-download"></i> Export Data
                                </button>
                                <button class="btn btn-secondary" id="importDataBtn">
                                    <i class="fas fa-upload"></i> Import Data
                                </button>
                                <button class="btn btn-danger" id="clearDataBtn">
                                    <i class="fas fa-trash"></i> Clear All Data
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <h3><i class="fas fa-info-circle"></i> About</h3>
                            <div class="about-info">
                                <p><strong>Slot Scatter Predictor Pro</strong></p>
                                <p>Version: 1.0.0</p>
                                <p>Advanced slot game analysis and prediction tool</p>
                                <p><em>For educational and research purposes only</em></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
