const puppeteer = require('puppeteer');
const UserAgent = require('user-agents');
const { EventEmitter } = require('events');

class ScrapingEngine extends EventEmitter {
    constructor(options = {}) {
        super();
        this.options = {
            headless: true,
            timeout: 30000,
            retryAttempts: 3,
            stealthMode: true,
            ...options
        };
        
        this.browser = null;
        this.page = null;
        this.isRunning = false;
        this.config = null;
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            errors: 0,
            lastUpdate: null
        };
        
        this.userAgent = new UserAgent();
    }

    async start(config) {
        try {
            this.config = config;
            this.isRunning = true;
            
            console.log('Starting scraping engine...');
            
            // Launch browser
            await this.launchBrowser();
            
            // Setup page
            await this.setupPage();
            
            // Navigate to target
            await this.navigateToTarget();
            
            // Start monitoring
            this.startMonitoring();
            
            return { success: true };
            
        } catch (error) {
            console.error('Failed to start scraping:', error);
            await this.cleanup();
            throw error;
        }
    }

    async launchBrowser() {
        const launchOptions = {
            headless: this.options.headless,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        };

        if (this.config.stealthMode) {
            launchOptions.args.push(
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images'
            );
        }

        this.browser = await puppeteer.launch(launchOptions);
        console.log('Browser launched successfully');
    }

    async setupPage() {
        this.page = await this.browser.newPage();
        
        // Set viewport
        await this.page.setViewport({
            width: 1366,
            height: 768,
            deviceScaleFactor: 1
        });

        // Set user agent
        const userAgent = this.getUserAgent();
        await this.page.setUserAgent(userAgent);
        
        // Set extra headers
        await this.page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        });

        if (this.config.stealthMode) {
            await this.enableStealthMode();
        }

        // Setup request/response monitoring
        this.page.on('response', (response) => {
            this.handleResponse(response);
        });

        this.page.on('console', (msg) => {
            console.log('Page console:', msg.text());
        });

        console.log('Page setup completed');
    }

    async enableStealthMode() {
        // Remove webdriver property
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        });

        // Mock plugins
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
        });

        // Mock languages
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        });

        // Override permissions
        await this.page.evaluateOnNewDocument(() => {
            const originalQuery = window.navigator.permissions.query;
            return window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Cypress.denied }) :
                    originalQuery(parameters)
            );
        });

        console.log('Stealth mode enabled');
    }

    getUserAgent() {
        switch (this.config.userAgent) {
            case 'chrome':
                return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
            case 'firefox':
                return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0';
            case 'safari':
                return 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15';
            case 'random':
                return this.userAgent.toString();
            default:
                return this.userAgent.toString();
        }
    }

    async navigateToTarget() {
        try {
            console.log(`Navigating to: ${this.config.targetUrl}`);
            
            await this.page.goto(this.config.targetUrl, {
                waitUntil: 'networkidle2',
                timeout: this.options.timeout
            });

            // Wait for page to be fully loaded
            await this.page.waitForTimeout(2000);
            
            // Check if page loaded successfully
            const title = await this.page.title();
            console.log(`Page loaded: ${title}`);
            
            this.emit('navigation', { success: true, title });
            
        } catch (error) {
            console.error('Navigation failed:', error);
            this.emit('navigation', { success: false, error: error.message });
            throw error;
        }
    }

    startMonitoring() {
        console.log('Starting game monitoring...');
        
        this.monitoringInterval = setInterval(async () => {
            if (!this.isRunning) return;
            
            try {
                await this.checkGameState();
            } catch (error) {
                console.error('Monitoring error:', error);
                this.emit('error', { error: error.message });
                
                // Retry logic
                this.stats.errors++;
                if (this.stats.errors > this.config.maxRetries) {
                    console.log('Max retries reached, stopping monitoring');
                    await this.stop();
                }
            }
        }, this.config.interval);
    }

    async checkGameState() {
        this.stats.totalRequests++;
        
        try {
            // Check if page is still responsive
            await this.page.evaluate(() => document.readyState);
            
            // Extract game data
            const gameData = await this.extractGameData();
            
            if (gameData) {
                this.stats.successfulRequests++;
                this.stats.lastUpdate = new Date();
                
                // Process the data
                this.processGameData(gameData);
            }
            
        } catch (error) {
            console.error('Game state check failed:', error);
            throw error;
        }
    }

    async extractGameData() {
        try {
            const data = await this.page.evaluate((selectors) => {
                const result = {};
                
                // Extract spin result
                if (selectors.spinResult) {
                    const spinElement = document.querySelector(selectors.spinResult);
                    result.spinResult = spinElement ? spinElement.textContent.trim() : null;
                }
                
                // Extract scatter information
                if (selectors.scatter) {
                    const scatterElements = document.querySelectorAll(selectors.scatter);
                    result.scatterCount = scatterElements.length;
                    result.hasScatter = scatterElements.length > 0;
                }
                
                // Extract balance
                if (selectors.balance) {
                    const balanceElement = document.querySelector(selectors.balance);
                    result.balance = balanceElement ? balanceElement.textContent.trim() : null;
                }
                
                // Extract any visible game state indicators
                result.gameState = {
                    spinning: document.querySelector('.spinning, .spin-active') !== null,
                    bonus: document.querySelector('.bonus-active, .free-spins') !== null,
                    timestamp: Date.now()
                };
                
                return result;
                
            }, this.config.selectors);
            
            return data;
            
        } catch (error) {
            console.error('Data extraction failed:', error);
            return null;
        }
    }

    processGameData(data) {
        // Detect new spins
        if (this.lastGameState && this.lastGameState.spinning && !data.gameState.spinning) {
            // Spin just completed
            this.emit('spin', {
                type: 'spin',
                isScatter: data.hasScatter,
                scatterCount: data.scatterCount,
                balance: data.balance,
                timestamp: new Date()
            });
        }
        
        // Store current state
        this.lastGameState = data.gameState;
        
        // Emit general update
        this.emit('update', {
            type: 'gameData',
            data: data,
            stats: this.stats
        });
    }

    handleResponse(response) {
        const url = response.url();
        const status = response.status();
        
        // Log important responses
        if (url.includes('api') || url.includes('game') || url.includes('spin')) {
            console.log(`Response: ${status} - ${url}`);
            
            // Try to extract game data from API responses
            if (status === 200 && response.headers()['content-type']?.includes('json')) {
                response.json().then(data => {
                    this.emit('apiResponse', { url, data });
                }).catch(() => {
                    // Ignore JSON parse errors
                });
            }
        }
    }

    async stop() {
        console.log('Stopping scraping engine...');
        
        this.isRunning = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        await this.cleanup();
        
        this.emit('stopped');
        
        return { success: true };
    }

    async cleanup() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            
            console.log('Cleanup completed');
            
        } catch (error) {
            console.error('Cleanup error:', error);
        }
    }

    getStatus() {
        return {
            isRunning: this.isRunning,
            stats: this.stats,
            config: this.config ? {
                targetUrl: this.config.targetUrl,
                interval: this.config.interval
            } : null
        };
    }

    // Test selectors without starting full monitoring
    async testSelectors(url, selectors) {
        try {
            const testBrowser = await puppeteer.launch({ headless: true });
            const testPage = await testBrowser.newPage();
            
            await testPage.goto(url, { waitUntil: 'networkidle2' });
            
            const results = await testPage.evaluate((sel) => {
                const test = {};
                
                Object.keys(sel).forEach(key => {
                    const elements = document.querySelectorAll(sel[key]);
                    test[key] = {
                        found: elements.length > 0,
                        count: elements.length,
                        sample: elements[0] ? elements[0].textContent.trim().substring(0, 50) : null
                    };
                });
                
                return test;
            }, selectors);
            
            await testBrowser.close();
            
            return { success: true, results };
            
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

module.exports = { ScrapingEngine };
