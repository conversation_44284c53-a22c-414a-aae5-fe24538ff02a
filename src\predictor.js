const { EventEmitter } = require('events');

class PredictionEngine extends EventEmitter {
    constructor(database) {
        super();
        this.database = database;
        this.settings = {
            model: 'hybrid',
            confidenceThreshold: 70,
            lookbackPeriod: 100,
            scatterRate: 0.03, // Default 3%
            adaptiveMode: true
        };
        
        this.patterns = new Map();
        this.streakAnalysis = {
            current: 0,
            longest: 0,
            average: 0,
            distribution: new Map()
        };
        
        this.predictionHistory = [];
        this.accuracy = {
            total: 0,
            correct: 0,
            percentage: 0
        };
    }

    async predict(currentData) {
        try {
            const prediction = await this.generatePrediction(currentData);
            
            // Store prediction for accuracy tracking
            this.predictionHistory.push({
                prediction,
                timestamp: new Date(),
                actualResult: null // Will be updated when result is known
            });
            
            // Emit prediction update
            this.emit('prediction', prediction);
            
            return prediction;
            
        } catch (error) {
            console.error('Prediction failed:', error);
            throw error;
        }
    }

    async generatePrediction(data) {
        const { totalSpins, totalScatters, currentStreak } = data;
        
        // Get historical data
        const history = await this.getHistoricalData();
        
        // Update pattern analysis
        this.updatePatterns(history);
        
        // Generate prediction based on selected model
        let prediction;
        
        switch (this.settings.model) {
            case 'statistical':
                prediction = this.statisticalPrediction(data);
                break;
            case 'pattern':
                prediction = this.patternPrediction(data, history);
                break;
            case 'ml':
                prediction = this.machineLearningPrediction(data, history);
                break;
            case 'hybrid':
            default:
                prediction = this.hybridPrediction(data, history);
                break;
        }
        
        // Add probability analysis
        prediction.probabilities = this.calculateProbabilities(prediction.spins);
        
        // Add insights
        prediction.insights = this.generateInsights(data, history, prediction);
        
        return prediction;
    }

    statisticalPrediction(data) {
        const { totalSpins, totalScatters, currentStreak } = data;
        
        // Calculate actual scatter rate
        const actualRate = totalSpins > 0 ? totalScatters / totalSpins : this.settings.scatterRate;
        
        // Expected spins until next scatter
        const expectedSpins = Math.round(1 / actualRate);
        
        // Adjust based on current streak
        let adjustedSpins = expectedSpins;
        
        if (currentStreak > expectedSpins) {
            // Longer than expected streak - slightly reduce prediction
            adjustedSpins = Math.max(1, expectedSpins - Math.floor(currentStreak * 0.1));
        } else if (currentStreak < expectedSpins * 0.5) {
            // Much shorter streak - slightly increase prediction
            adjustedSpins = expectedSpins + Math.floor((expectedSpins - currentStreak) * 0.2);
        }
        
        const confidence = this.calculateConfidence(actualRate, currentStreak, expectedSpins);
        
        return {
            spins: adjustedSpins,
            confidence: Math.round(confidence),
            method: 'statistical',
            factors: {
                actualRate: actualRate.toFixed(4),
                expectedSpins,
                streakAdjustment: adjustedSpins - expectedSpins
            }
        };
    }

    patternPrediction(data, history) {
        const { currentStreak } = data;
        
        // Analyze patterns in historical data
        const patterns = this.analyzePatterns(history);
        
        // Find similar situations
        const similarSituations = this.findSimilarSituations(currentStreak, history);
        
        // Calculate prediction based on patterns
        let predictedSpins = 0;
        let confidence = 0;
        
        if (similarSituations.length > 0) {
            const outcomes = similarSituations.map(s => s.spinsToScatter);
            predictedSpins = Math.round(outcomes.reduce((a, b) => a + b, 0) / outcomes.length);
            confidence = Math.min(90, 50 + (similarSituations.length * 5));
        } else {
            // Fallback to statistical method
            return this.statisticalPrediction(data);
        }
        
        return {
            spins: predictedSpins,
            confidence: Math.round(confidence),
            method: 'pattern',
            factors: {
                similarSituations: similarSituations.length,
                patternStrength: patterns.strength,
                averageOutcome: predictedSpins
            }
        };
    }

    machineLearningPrediction(data, history) {
        // Simplified ML approach using weighted features
        const features = this.extractFeatures(data, history);
        const weights = this.getMLWeights();
        
        // Calculate weighted prediction
        let prediction = 0;
        let totalWeight = 0;
        
        Object.keys(features).forEach(key => {
            if (weights[key]) {
                prediction += features[key] * weights[key];
                totalWeight += weights[key];
            }
        });
        
        prediction = totalWeight > 0 ? Math.round(prediction / totalWeight) : 33;
        
        const confidence = this.calculateMLConfidence(features, history);
        
        return {
            spins: Math.max(1, prediction),
            confidence: Math.round(confidence),
            method: 'machine_learning',
            factors: {
                features,
                weights,
                modelConfidence: confidence
            }
        };
    }

    hybridPrediction(data, history) {
        // Combine multiple prediction methods
        const statistical = this.statisticalPrediction(data);
        const pattern = this.patternPrediction(data, history);
        const ml = this.machineLearningPrediction(data, history);
        
        // Weight the predictions based on confidence and data availability
        const predictions = [statistical, pattern, ml];
        const weights = predictions.map(p => p.confidence / 100);
        const totalWeight = weights.reduce((a, b) => a + b, 0);
        
        // Calculate weighted average
        const weightedSpins = predictions.reduce((sum, pred, i) => {
            return sum + (pred.spins * weights[i]);
        }, 0) / totalWeight;
        
        const averageConfidence = predictions.reduce((sum, pred) => {
            return sum + pred.confidence;
        }, 0) / predictions.length;
        
        return {
            spins: Math.round(weightedSpins),
            confidence: Math.round(averageConfidence),
            method: 'hybrid',
            factors: {
                statistical: statistical.spins,
                pattern: pattern.spins,
                ml: ml.spins,
                weights: weights.map(w => Math.round(w * 100))
            }
        };
    }

    calculateProbabilities(predictedSpins) {
        const rate = this.settings.scatterRate;
        
        return {
            next5: Math.round((1 - Math.pow(1 - rate, 5)) * 100),
            next10: Math.round((1 - Math.pow(1 - rate, 10)) * 100),
            next20: Math.round((1 - Math.pow(1 - rate, 20)) * 100),
            next50: Math.round((1 - Math.pow(1 - rate, 50)) * 100),
            predicted: Math.round((1 - Math.pow(1 - rate, predictedSpins)) * 100)
        };
    }

    calculateConfidence(actualRate, currentStreak, expectedSpins) {
        let confidence = 70; // Base confidence
        
        // Adjust based on data quality
        const dataQuality = Math.min(1, actualRate * 1000); // More data = higher confidence
        confidence += dataQuality * 20;
        
        // Adjust based on streak position
        const streakRatio = currentStreak / expectedSpins;
        if (streakRatio > 1.5) {
            confidence += 10; // High confidence when streak is long
        } else if (streakRatio < 0.5) {
            confidence -= 10; // Lower confidence when streak is short
        }
        
        return Math.max(10, Math.min(95, confidence));
    }

    analyzePatterns(history) {
        const patterns = {
            streakLengths: [],
            timePatterns: new Map(),
            sequencePatterns: [],
            strength: 0
        };
        
        if (history.length < 10) {
            return patterns;
        }
        
        // Analyze streak lengths
        let currentStreak = 0;
        for (const record of history) {
            if (record.type === 'scatter') {
                if (currentStreak > 0) {
                    patterns.streakLengths.push(currentStreak);
                }
                currentStreak = 0;
            } else {
                currentStreak++;
            }
        }
        
        // Calculate pattern strength
        if (patterns.streakLengths.length > 0) {
            const variance = this.calculateVariance(patterns.streakLengths);
            const mean = patterns.streakLengths.reduce((a, b) => a + b, 0) / patterns.streakLengths.length;
            patterns.strength = Math.max(0, 100 - (variance / mean) * 10);
        }
        
        return patterns;
    }

    findSimilarSituations(currentStreak, history) {
        const similar = [];
        const tolerance = Math.max(2, Math.floor(currentStreak * 0.2));
        
        let streak = 0;
        for (let i = 0; i < history.length; i++) {
            const record = history[i];
            
            if (record.type === 'scatter') {
                if (Math.abs(streak - currentStreak) <= tolerance) {
                    similar.push({
                        streak,
                        spinsToScatter: 1, // Next spin was scatter
                        timestamp: record.timestamp
                    });
                }
                streak = 0;
            } else {
                streak++;
            }
        }
        
        return similar;
    }

    extractFeatures(data, history) {
        const { totalSpins, totalScatters, currentStreak } = data;
        
        return {
            currentStreak: currentStreak / 100, // Normalized
            scatterRate: totalSpins > 0 ? totalScatters / totalSpins : 0.03,
            recentActivity: this.getRecentActivity(history),
            timeOfDay: new Date().getHours() / 24,
            dayOfWeek: new Date().getDay() / 7,
            sessionLength: totalSpins / 1000 // Normalized
        };
    }

    getMLWeights() {
        // Simplified weights - in real ML, these would be learned
        return {
            currentStreak: 0.4,
            scatterRate: 0.3,
            recentActivity: 0.15,
            timeOfDay: 0.05,
            dayOfWeek: 0.05,
            sessionLength: 0.05
        };
    }

    calculateMLConfidence(features, history) {
        let confidence = 60;
        
        // More data = higher confidence
        confidence += Math.min(20, history.length / 10);
        
        // Consistent patterns = higher confidence
        if (features.scatterRate > 0.01 && features.scatterRate < 0.1) {
            confidence += 10;
        }
        
        return Math.max(30, Math.min(90, confidence));
    }

    getRecentActivity(history) {
        const recent = history.slice(-20);
        const scatters = recent.filter(r => r.type === 'scatter').length;
        return recent.length > 0 ? scatters / recent.length : 0;
    }

    generateInsights(data, history, prediction) {
        const insights = [];
        
        const { currentStreak, totalSpins, totalScatters } = data;
        const actualRate = totalSpins > 0 ? (totalScatters / totalSpins) * 100 : 3;
        
        // Streak analysis
        if (currentStreak > 50) {
            insights.push(`🔥 Long streak detected (${currentStreak} spins). Scatter probability increasing.`);
        } else if (currentStreak < 10) {
            insights.push(`🎯 Short streak (${currentStreak} spins). Normal probability range.`);
        }
        
        // Rate analysis
        if (actualRate > 5) {
            insights.push(`📈 Above average scatter rate (${actualRate.toFixed(1)}%). Lucky session!`);
        } else if (actualRate < 2) {
            insights.push(`📉 Below average scatter rate (${actualRate.toFixed(1)}%). Due for improvement.`);
        }
        
        // Prediction confidence
        if (prediction.confidence > 80) {
            insights.push(`✅ High confidence prediction based on strong data patterns.`);
        } else if (prediction.confidence < 50) {
            insights.push(`⚠️ Low confidence - limited data or irregular patterns detected.`);
        }
        
        return insights.join('<br>');
    }

    updatePatterns(history) {
        // Update internal pattern analysis
        this.patterns.clear();
        
        if (history.length > 0) {
            const analysis = this.analyzePatterns(history);
            this.patterns.set('streaks', analysis.streakLengths);
            this.patterns.set('strength', analysis.strength);
        }
    }

    async getHistoricalData() {
        try {
            if (this.database) {
                return await this.database.getHistory(this.settings.lookbackPeriod);
            }
            return [];
        } catch (error) {
            console.error('Failed to get historical data:', error);
            return [];
        }
    }

    calculateVariance(numbers) {
        if (numbers.length === 0) return 0;
        
        const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
        const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
        
        return variance;
    }

    async updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.emit('settingsUpdated', this.settings);
        return this.settings;
    }

    getAccuracy() {
        return this.accuracy;
    }

    // Update prediction accuracy when actual results are known
    updateAccuracy(predictedSpins, actualSpins) {
        this.accuracy.total++;
        
        const tolerance = Math.max(2, Math.floor(predictedSpins * 0.2));
        if (Math.abs(predictedSpins - actualSpins) <= tolerance) {
            this.accuracy.correct++;
        }
        
        this.accuracy.percentage = (this.accuracy.correct / this.accuracy.total) * 100;
        
        this.emit('accuracyUpdated', this.accuracy);
    }
}

module.exports = { PredictionEngine };
